# DuckDB Nested Types Technical Reference

## Overview

This document provides comprehensive technical details about DuckDB's internal representation of nested data structures (LIST/Array and STRUCT types), based on analysis of the DuckDB Rust API codebase. This reference enables implementation of streaming JSON extensions that handle complex nested structures using DuckDB's native type system.

## Core Vector Type Hierarchy

### Vector Types

```rust
// Core vector types from duckdb-rs/crates/duckdb/src/core/vector.rs
pub struct FlatVector    // For primitive types (i64, f64, String, etc.)
pub struct ListVector    // For LIST types (variable-length arrays)
pub struct StructVector  // For STRUCT types (named field collections)
pub struct ArrayVector   // For fixed-size arrays
```

### Vector Creation Pattern

```rust
// From DataChunkHandle
let chunk = DataChunkHandle::new(&[logical_type]);
let list_vector = chunk.list_vector(0);      // Get ListVector
let struct_vector = chunk.struct_vector(0);  // Get StructVector
let flat_vector = chunk.flat_vector(0);      // Get FlatVector
```

## List/Array Types Implementation

### ListVector Core Methods

```rust
impl ListVector {
    // Memory management
    pub fn len(&self) -> usize                           // Current number of entries
    pub fn is_empty(&self) -> bool                       // Check if empty
    fn reserve(&self, capacity: usize)                   // Reserve child capacity
    pub fn set_len(&self, new_len: usize)               // Set list size
    
    // Child vector access (CRITICAL: Use correct method for element type)
    pub fn child(&self, capacity: usize) -> FlatVector           // For primitives ONLY
    pub fn struct_child(&self, capacity: usize) -> StructVector // For STRUCT elements
    pub fn list_child(&self) -> ListVector                      // For nested lists
    pub fn array_child(&self) -> ArrayVector                    // For array elements
    
    // Data manipulation
    pub fn set_entry(&mut self, idx: usize, offset: usize, length: usize)  // Set list entry
    pub fn set_child<T: Copy>(&self, data: &[T])               // Set primitive data
    pub fn set_null(&mut self, row: usize)                     // Mark row as null
}
```

### Memory Layout and Allocation

```rust
// Memory allocation pattern for ListVector
let total_capacity = calculate_total_elements_needed();
let mut list_vector = chunk.list_vector(0);

// CRITICAL: Reserve capacity before accessing child
list_vector.reserve(total_capacity);  // Called internally by child() methods

// Access child vector with proper type
let mut child_vector = match element_type {
    JsonValueType::Number => list_vector.child(total_capacity),        // FlatVector
    JsonValueType::Object(_) => list_vector.struct_child(total_capacity), // StructVector
    JsonValueType::Array(_) => list_vector.list_child(),              // ListVector
};
```

### List Entry Management

```rust
// List entries define offset and length for each list element
// Critical for variable-length arrays
let mut offset = 0;
for (row_idx, array_data) in arrays.iter().enumerate() {
    let array_length = array_data.len();
    list_vector.set_entry(row_idx, offset, array_length);
    offset += array_length;  // Cumulative offset for next array
}
```

## Struct Types Implementation

### StructVector Core Methods

```rust
impl StructVector {
    // Field access by index
    pub fn child(&self, idx: usize, capacity: usize) -> FlatVector    // Get field vector
    pub fn struct_vector_child(&self, idx: usize) -> StructVector     // Nested struct field
    pub fn list_vector_child(&self, idx: usize) -> ListVector         // List field
    pub fn array_vector_child(&self, idx: usize) -> ArrayVector       // Array field
    
    // Metadata access
    pub fn num_children(&self) -> usize                               // Number of fields
    pub fn child_name(&self, idx: usize) -> DuckDbString             // Field name
    pub fn logical_type(&self) -> LogicalTypeHandle                  // Type information
    
    // Data manipulation
    pub fn set_null(&mut self, row: usize)                           // Mark row as null
}
```

### Field Storage and Access

```rust
// STRUCT field insertion pattern
let mut struct_vector = list_vector.struct_child(capacity);

for (field_idx, field_data) in struct_fields.iter().enumerate() {
    let mut field_vector = struct_vector.child(field_idx, row_count);
    
    match field_data.field_type {
        JsonValueType::Number => {
            // Insert numeric data
            field_vector.as_mut_slice::<f64>()[row_idx] = value;
        }
        JsonValueType::String => {
            // Insert string data
            field_vector.insert(row_idx, &string_value);
        }
        // Handle other types...
    }
}
```

## Logical Type Creation

### Type Construction API

```rust
// From duckdb-rs/crates/duckdb/src/core/logical_type.rs
impl LogicalTypeHandle {
    // Primitive types
    pub fn from(type_id: LogicalTypeId) -> Self
    
    // Complex type creation
    pub fn list(child_type: &Self) -> Self                    // LIST[child_type]
    pub fn array(child_type: &Self, size: u64) -> Self       // ARRAY[child_type, size]
    pub fn struct_type(fields: &[(&str, Self)]) -> Self      // STRUCT with named fields
    pub fn map(key: &Self, value: &Self) -> Self             // MAP[key, value]
    
    // Type introspection
    pub fn id(&self) -> LogicalTypeId                        // Get type ID
    pub fn num_children(&self) -> usize                      // Number of child types
    pub fn child(&self, idx: usize) -> Self                  // Get child type
    pub fn child_name(&self, idx: usize) -> String           // Get field name (STRUCT)
}
```

### Nested Type Creation Examples

```rust
// Create LIST[STRUCT] type for JSON arrays of objects
let struct_fields = vec![
    ("name", LogicalTypeHandle::from(LogicalTypeId::Varchar)),
    ("age", LogicalTypeHandle::from(LogicalTypeId::Integer)),
    ("active", LogicalTypeHandle::from(LogicalTypeId::Boolean)),
];
let struct_type = LogicalTypeHandle::struct_type(&struct_fields);
let list_struct_type = LogicalTypeHandle::list(&struct_type);

// Create multi-dimensional array: LIST[LIST[NUMBER]]
let number_type = LogicalTypeHandle::from(LogicalTypeId::Double);
let inner_list_type = LogicalTypeHandle::list(&number_type);
let outer_list_type = LogicalTypeHandle::list(&inner_list_type);
```

## Arbitrary Nesting Support

### Recursive Type Definitions

```rust
// Recursive function for arbitrary depth JSON schema conversion
fn json_type_to_duckdb_type(json_type: &JsonValueType) -> Result<LogicalTypeHandle, Error> {
    match json_type {
        JsonValueType::Number => Ok(LogicalTypeHandle::from(LogicalTypeId::Double)),
        JsonValueType::String => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonValueType::Boolean => Ok(LogicalTypeHandle::from(LogicalTypeId::Boolean)),
        JsonValueType::Null => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        
        // Recursive array handling
        JsonValueType::Array(element_type) => {
            let element_logical_type = json_type_to_duckdb_type(element_type)?;
            Ok(LogicalTypeHandle::list(&element_logical_type))
        }
        
        // Recursive object handling
        JsonValueType::Object(fields) => {
            let mut struct_fields = Vec::new();
            for field in fields {
                let field_type = json_type_to_duckdb_type(&field.value_type)?;
                struct_fields.push((field.name.as_str(), field_type));
            }
            Ok(LogicalTypeHandle::struct_type(&struct_fields))
        }
    }
}
```

### Memory Efficiency Strategies

```rust
// Batch processing for memory efficiency
fn process_json_batch(
    chunk: &mut DataChunkHandle,
    json_data: &[JsonValue],
    schema: &JsonSchema,
) -> Result<(), Error> {
    // Calculate total memory requirements upfront
    let total_capacity = calculate_total_capacity(json_data, schema);
    
    // Process all data in single batch to minimize allocations
    let mut list_vector = chunk.list_vector(0);
    
    match schema.root_type {
        JsonValueType::Array(ref element_type) => {
            process_array_batch(&mut list_vector, json_data, element_type, total_capacity)?;
        }
        JsonValueType::Object(ref fields) => {
            process_struct_batch(&mut list_vector, json_data, fields)?;
        }
        _ => return Err("Unsupported root type".into()),
    }
    
    Ok(())
}
```

## Vector Implementation Details

### Low-Level C API Functions

```rust
// Critical C API functions from libduckdb-sys
unsafe extern "C" {
    // Vector creation and destruction
    pub fn duckdb_create_vector(type_: duckdb_logical_type, capacity: idx_t) -> duckdb_vector;
    pub fn duckdb_destroy_vector(vector: *mut duckdb_vector);

    // List vector operations
    pub fn duckdb_list_vector_get_child(vector: duckdb_vector) -> duckdb_vector;
    pub fn duckdb_list_vector_get_size(vector: duckdb_vector) -> idx_t;
    pub fn duckdb_list_vector_set_size(vector: duckdb_vector, size: idx_t) -> duckdb_state;
    pub fn duckdb_list_vector_reserve(vector: duckdb_vector, required_capacity: idx_t) -> duckdb_state;

    // Struct vector operations
    pub fn duckdb_struct_vector_get_child(vector: duckdb_vector, index: idx_t) -> duckdb_vector;
    pub fn duckdb_struct_type_child_count(type_: duckdb_logical_type) -> idx_t;
    pub fn duckdb_struct_type_child_name(type_: duckdb_logical_type, index: idx_t) -> *mut c_char;

    // Data assignment
    pub fn duckdb_vector_assign_string_element(vector: duckdb_vector, index: idx_t, str_: *const c_char);
    pub fn duckdb_vector_assign_string_element_len(vector: duckdb_vector, index: idx_t, str_: *const c_char, str_len: idx_t);

    // Validity/null handling
    pub fn duckdb_vector_ensure_validity_writable(vector: duckdb_vector);
    pub fn duckdb_vector_get_validity(vector: duckdb_vector) -> *mut u64;
    pub fn duckdb_validity_set_row_invalid(validity: *mut u64, row: idx_t);
    pub fn duckdb_validity_row_is_valid(validity: *mut u64, row: idx_t) -> bool;
}
```

### Memory Management Best Practices

```rust
// Capacity calculation for nested structures
fn calculate_nested_capacity(json_data: &[JsonValue]) -> usize {
    json_data.iter()
        .map(|value| match value {
            JsonValue::Array(arr) => arr.len(),
            JsonValue::Object(obj) => obj.len(),
            _ => 1,
        })
        .sum()
}

// Safe vector access with capacity validation
fn safe_vector_access<T>(
    vector: &mut FlatVector,
    index: usize,
    capacity: usize,
) -> Result<&mut T, Error> {
    if index >= capacity {
        return Err(Error::InvalidColumnIndex(index));
    }

    let slice = vector.as_mut_slice::<T>();
    if slice.len() <= index {
        return Err(Error::InvalidColumnIndex(index));
    }

    Ok(&mut slice[index])
}
```

### Batch Processing Mechanisms

```rust
// Efficient batch processing for large datasets
struct BatchProcessor {
    chunk_size: usize,
    current_chunk: DataChunkHandle,
    processed_rows: usize,
}

impl BatchProcessor {
    fn new(schema: &[LogicalTypeHandle], chunk_size: usize) -> Self {
        Self {
            chunk_size,
            current_chunk: DataChunkHandle::new(schema),
            processed_rows: 0,
        }
    }

    fn process_batch(&mut self, json_batch: &[JsonValue]) -> Result<(), Error> {
        let batch_size = json_batch.len().min(self.chunk_size);
        self.current_chunk.set_len(batch_size);

        // Process each column
        for (col_idx, json_values) in json_batch.iter().enumerate() {
            self.process_column(col_idx, json_values)?;
        }

        self.processed_rows += batch_size;
        Ok(())
    }

    fn process_column(&mut self, col_idx: usize, json_values: &JsonValue) -> Result<(), Error> {
        match json_values {
            JsonValue::Array(arr) => self.process_list_column(col_idx, arr),
            JsonValue::Object(obj) => self.process_struct_column(col_idx, obj),
            _ => self.process_primitive_column(col_idx, json_values),
        }
    }
}
```

## Error Handling and Validation

### Vector Validation Patterns

```rust
// Comprehensive validation for nested vectors
fn validate_nested_vector(
    vector: &ListVector,
    expected_type: &LogicalTypeHandle,
    max_depth: usize,
) -> Result<(), Error> {
    if max_depth == 0 {
        return Err(Error::InvalidQuery); // Prevent infinite recursion
    }

    // Validate vector structure
    let vector_type = vector.logical_type();
    if vector_type.id() != LogicalTypeId::List {
        return Err(Error::InvalidColumnType(0, "Expected LIST type".to_string(), Type::Text));
    }

    // Validate child type recursively
    let child_type = vector_type.child(0);
    match child_type.id() {
        LogicalTypeId::Struct => {
            let struct_vector = vector.struct_child(1);
            validate_struct_vector(&struct_vector, &child_type, max_depth - 1)?;
        }
        LogicalTypeId::List => {
            let nested_list = vector.list_child();
            validate_nested_vector(&nested_list, &child_type, max_depth - 1)?;
        }
        _ => {
            // Primitive type - validate compatibility
            validate_primitive_type(&child_type, expected_type)?;
        }
    }

    Ok(())
}

fn validate_struct_vector(
    vector: &StructVector,
    expected_type: &LogicalTypeHandle,
    max_depth: usize,
) -> Result<(), Error> {
    let num_fields = vector.num_children();
    let expected_fields = expected_type.num_children();

    if num_fields != expected_fields {
        return Err(Error::InvalidColumnType(
            0,
            format!("Expected {} fields, got {}", expected_fields, num_fields),
            Type::Text,
        ));
    }

    // Validate each field recursively
    for field_idx in 0..num_fields {
        let field_name = vector.child_name(field_idx);
        let expected_field_name = expected_type.child_name(field_idx);

        if field_name != expected_field_name {
            return Err(Error::InvalidColumnName(format!(
                "Expected field '{}', got '{}'",
                expected_field_name, field_name
            )));
        }

        let field_type = expected_type.child(field_idx);
        match field_type.id() {
            LogicalTypeId::List => {
                let list_vector = vector.list_vector_child(field_idx);
                validate_nested_vector(&list_vector, &field_type, max_depth - 1)?;
            }
            LogicalTypeId::Struct => {
                let nested_struct = vector.struct_vector_child(field_idx);
                validate_struct_vector(&nested_struct, &field_type, max_depth - 1)?;
            }
            _ => {
                // Primitive field - basic validation
                let field_vector = vector.child(field_idx, 1);
                validate_primitive_vector(&field_vector, &field_type)?;
            }
        }
    }

    Ok(())
}
```

## Implementation Patterns for Streaming JSON

### Multi-Dimensional Array Handling

```rust
// Pattern for handling arbitrary depth arrays
fn insert_multidimensional_array(
    list_vector: &mut ListVector,
    json_arrays: &[JsonValue],
    element_type: &JsonValueType,
    depth: usize,
) -> Result<(), Error> {
    match element_type {
        JsonValueType::Array(inner_type) => {
            // Nested array - recurse deeper
            let mut nested_list = list_vector.list_child();

            let mut global_offset = 0;
            for (row_idx, json_array) in json_arrays.iter().enumerate() {
                if let JsonValue::Array(inner_arrays) = json_array {
                    list_vector.set_entry(row_idx, global_offset, inner_arrays.len());

                    insert_multidimensional_array(
                        &mut nested_list,
                        inner_arrays,
                        inner_type,
                        depth + 1,
                    )?;

                    global_offset += inner_arrays.len();
                }
            }
        }
        _ => {
            // Base case - primitive elements
            insert_primitive_array(list_vector, json_arrays, element_type)?;
        }
    }

    Ok(())
}
```

### Complex Nested Structure Example

```rust
// Real-world example: Processing nested JSON with mixed types
// JSON: [{"users": [{"name": "John", "scores": [95, 87]}]}]

fn process_complex_json_structure() -> Result<(), Error> {
    // 1. Define schema
    let score_list_type = LogicalTypeHandle::list(
        &LogicalTypeHandle::from(LogicalTypeId::Integer)
    );

    let user_struct_type = LogicalTypeHandle::struct_type(&[
        ("name", LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        ("scores", score_list_type),
    ]);

    let users_list_type = LogicalTypeHandle::list(&user_struct_type);

    let root_struct_type = LogicalTypeHandle::struct_type(&[
        ("users", users_list_type),
    ]);

    let root_list_type = LogicalTypeHandle::list(&root_struct_type);

    // 2. Create data chunk
    let chunk = DataChunkHandle::new(&[root_list_type]);
    let mut root_list_vector = chunk.list_vector(0);

    // 3. Process data
    let mut root_struct_vector = root_list_vector.struct_child(1); // 1 root object
    let mut users_list_vector = root_struct_vector.list_vector_child(0); // "users" field

    // Set up users array
    users_list_vector.set_entry(0, 0, 1); // 1 user
    let mut user_struct_vector = users_list_vector.struct_child(1);

    // Insert user name
    let mut name_vector = user_struct_vector.child(0, 1); // "name" field
    name_vector.insert(0, "John");

    // Insert user scores
    let mut scores_list_vector = user_struct_vector.list_vector_child(1); // "scores" field
    scores_list_vector.set_entry(0, 0, 2); // 2 scores
    let mut scores_vector = scores_list_vector.child(2); // FlatVector for integers
    scores_vector.as_mut_slice::<i32>()[0] = 95;
    scores_vector.as_mut_slice::<i32>()[1] = 87;

    // 4. Finalize
    root_list_vector.set_entry(0, 0, 1); // 1 root object
    chunk.set_len(1);

    Ok(())
}
```

### Performance Optimization Patterns

```rust
// Memory-efficient streaming pattern
struct StreamingProcessor {
    buffer_size: usize,
    type_cache: HashMap<String, LogicalTypeHandle>,
    vector_pool: Vec<DataChunkHandle>,
}

impl StreamingProcessor {
    fn process_stream<R: Read>(
        &mut self,
        reader: R,
        schema: &JsonSchema,
    ) -> Result<Vec<DataChunkHandle>, Error> {
        let mut json_reader = JsonStreamReader::new(reader);
        let mut chunks = Vec::new();
        let mut current_batch = Vec::new();

        json_reader.begin_array()?;
        while json_reader.has_next()? {
            let json_value = self.read_json_value(&mut json_reader)?;
            current_batch.push(json_value);

            if current_batch.len() >= self.buffer_size {
                let chunk = self.process_batch(&current_batch, schema)?;
                chunks.push(chunk);
                current_batch.clear();
            }
        }

        // Process remaining items
        if !current_batch.is_empty() {
            let chunk = self.process_batch(&current_batch, schema)?;
            chunks.push(chunk);
        }

        json_reader.end_array()?;
        Ok(chunks)
    }

    fn process_batch(
        &mut self,
        batch: &[JsonValue],
        schema: &JsonSchema,
    ) -> Result<DataChunkHandle, Error> {
        let logical_types = self.get_or_create_types(schema)?;
        let mut chunk = self.get_or_create_chunk(&logical_types);

        chunk.set_len(batch.len());

        // Process each column efficiently
        for (col_idx, column_schema) in schema.columns.iter().enumerate() {
            self.process_column(&mut chunk, col_idx, batch, column_schema)?;
        }

        Ok(chunk)
    }
}
```

## Critical Anti-Patterns to Avoid

### Memory Management Anti-Patterns

```rust
// ❌ WRONG: Creating vectors without proper capacity
let mut list_vector = chunk.list_vector(0);
let child_vector = list_vector.child(0); // No capacity reserved!

// ✅ CORRECT: Reserve capacity first
let total_capacity = calculate_capacity(data);
let mut list_vector = chunk.list_vector(0);
let child_vector = list_vector.child(total_capacity);

// ❌ WRONG: Using wrong vector type for complex elements
let child_vector = list_vector.child(capacity); // Returns FlatVector
// Trying to insert STRUCT data into FlatVector fails

// ✅ CORRECT: Use appropriate vector type
let struct_vector = list_vector.struct_child(capacity); // Returns StructVector

// ❌ WRONG: VARCHAR fallbacks for structured data
let varchar_type = LogicalTypeHandle::from(LogicalTypeId::Varchar);
// Loses all type information and structure

// ✅ CORRECT: Proper STRUCT types
let struct_type = LogicalTypeHandle::struct_type(&fields);
```

### Type System Violations

```rust
// ❌ WRONG: Hardcoded depth limits
if depth > 5 {
    return Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)); // Fallback
}

// ✅ CORRECT: Recursive handling without arbitrary limits
fn handle_arbitrary_depth(json_type: &JsonValueType) -> Result<LogicalTypeHandle, Error> {
    match json_type {
        JsonValueType::Array(element_type) => {
            let element_logical_type = handle_arbitrary_depth(element_type)?;
            Ok(LogicalTypeHandle::list(&element_logical_type))
        }
        JsonValueType::Object(fields) => {
            let struct_fields: Result<Vec<_>, _> = fields.iter()
                .map(|field| {
                    let field_type = handle_arbitrary_depth(&field.value_type)?;
                    Ok((field.name.as_str(), field_type))
                })
                .collect();
            Ok(LogicalTypeHandle::struct_type(&struct_fields?))
        }
        _ => Ok(primitive_type_mapping(json_type)),
    }
}
```

## Summary

This reference provides the technical foundation for implementing DuckDB extensions that handle complex nested JSON structures with:

1. **Proper Type System Usage**: Always use appropriate vector types (StructVector for objects, ListVector for arrays)
2. **Memory Efficiency**: Calculate capacity upfront and reserve memory appropriately
3. **Arbitrary Nesting Support**: Use recursive patterns without hardcoded depth limits
4. **Error Handling**: Validate types and data integrity at each level
5. **Performance Optimization**: Batch processing and memory pooling for large datasets

The key insight is that DuckDB's vector system is designed for structured data - leveraging this properly eliminates the need for VARCHAR fallbacks and enables true streaming processing of complex JSON with full type preservation.
```
```
